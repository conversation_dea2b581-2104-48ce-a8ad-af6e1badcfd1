# Bot Review Assistant

An intelligent code review assistant powered by <PERSON><PERSON><PERSON><PERSON>, LangGraph, and local LLM models. This tool analyzes pull request diffs and provides comprehensive feedback on code quality, naming conventions, syntax, and logic.

## Features

- **Multi-language Support**: Automatically detects programming language
- **Code Structure Analysis**: Scans for classes, functions, and variables
- **Naming Convention Checks**: Reviews naming patterns and conventions
- **Syntax Validation**: Checks for syntax errors and issues
- **Logic Review**: Analyzes code logic and provides suggestions
- **Interactive UI**: Clean Streamlit interface with organized results
- **RAG Architecture**: Uses FAISS vector store for context-aware reviews

## Quick Start

### Prerequisites

- Python 3.13
- At least 8GB RAM (for local LLM)
- GGUF model file (DeepSeek-Coder recommended)

### Installation

**Step 1**: Clone and setup virtual environment
```bash
cd bot-assistance
python -m venv venv
source venv/bin/activate.bat  # On Windows
```

**Step 2**: Install dependencies
```bash
pip install -r requirements.txt
```

**Step 3**: Download model
- Download `DeepSeek-Coder-V2-Lite-Instruct-Q4_K_M.gguf`
- Download `all-MiniLM-L6-v2-Q4_K_M.gguf`
- Place it in `./models/` directory

**Step 4**: Configure environment
```bash
cp .env.example .env
# Then filled .env
```

**Step 5**: Run the application
```bash
streamlit run main.py
```

## Usage

1. **Open the web interface** (usually http://localhost:8501)
2. **Paste your PR diff** in the text area
3. **Click "Review Code"** to start analysis
4. **View organized results** in different tabs:
   - Summary: Overall assessment
   - Language: Detected programming language
   - Structure: Found classes, functions, variables
   - Naming: Naming convention feedback
   - Syntax: Syntax validation results
   - Logic: Logic review and suggestions

## Architecture

```
├── code_review_agent/
│   ├── code_review_agent.py    # Main pipeline orchestrator
│   └── utils/
│       ├── llm.py             # Local LLM loading
│       ├── nodes.py           # LangGraph workflow nodes
│       └── tools.py           # Review tools and functions
├── main.py                    # Streamlit UI
├── agent.py                   # CLI interface
└── requirements.txt           # Dependencies
```

## 🔧 Configuration

Edit `.env` file to customize:

## 🛠️ Development

### Running Tests
```bash
python agent.py  # Test with sample diff
```

### Adding New Review Tools
1. Add new tool function in `utils/tools.py`
2. Add corresponding node in `utils/nodes.py`
3. Update workflow graph edges

## Example Diff Format

```diff
TBD
```